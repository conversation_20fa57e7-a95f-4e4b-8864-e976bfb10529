/* Footer Styles */
.footer {
  background-color: var(--bg);
  border-top: 1px solid var(--border-soft);
  padding: 0;
  position: relative;
  overflow: hidden;
  min-height: 400px;
}

.container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* Main footer content layout with animation on left and content on right */
.footerMainContent {
  display: flex;
  align-items: stretch;
  height: 100%;
  min-height: 400px;
  position: relative;
}

/* Lottie Animation Container */
.footerAnimation {
  position: absolute;
  left: -50%;
  top: 0;
  min-width: 50vw;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.lottieAnimation {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.6;
  filter: brightness(1.1);
}

/* Footer content wrapper - aligned to the right */
.footerContentWrapper {
  margin-left: auto;
  width: 100%;
  max-width: 500px;
  padding: 4rem 2rem 4rem 4rem;
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Grid layout for footer columns - vertical stack */
.footerGrid {
  display: flex;
  flex-direction: column;
  gap: 3rem;
  width: 100%;
}

/* Brand Column */
.brandColumn {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: flex-start;
  text-align: left;
}

.footerBrand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
  /* transition: all 0.3s ease; */
}

.footerBrand:hover {
  transform: translateY(-2px);
}

.footerLogo {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--secondary) 0%, var(--primary) 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(61, 158, 238, 0.3);
}

.logoText {
  font-family: var(--font-bwgradual);
  font-size: 1.125rem;
  font-weight: 700;
  color: #fff;
}

.brandName {
  font-family: var(--font-bwgradual);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--fg);
}

.footerTagline {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: var(--leading-relaxed);
  margin: 0;
  max-width: 320px;
}

/* Social Icons */
.socialIcons {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-start;
}

.socialLink {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--surface-dim);
  border: 1px solid var(--border-soft);
  border-radius: 10px;
  color: var(--text-secondary);
  text-decoration: none;
  /* transition: all 0.3s ease; */
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.socialLink:hover {
  background: var(--secondary);
  border-color: var(--secondary);
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(61, 158, 238, 0.4);
}

.socialLink i {
  font-size: 1rem;
}

/* Footer Columns - organized vertically */
.footerColumn {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-start;
  text-align: left;
}

.columnTitle {
  font-family: var(--font-bwgradual);
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-tertiary);
  letter-spacing: 0.1em;
  text-transform: uppercase;
  margin: 0 0 0.5rem 0;
}

.linksList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-start;
}

.footerLink {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.875rem;
  line-height: var(--leading-relaxed);
  /* transition: all 0.3s ease; */
  position: relative;
  padding: 0.25rem 0;
}

.footerLink:hover {
  color: var(--fg);
  transform: translateX(4px);
}

.footerLink::before {
  content: "";
  position: absolute;
  left: -12px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  background: var(--secondary);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.footerLink:hover::before {
  opacity: 1;
}

/* Footer Bottom */
.footerBottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid var(--border-soft);
  gap: 1rem;
  width: 100%;
  margin-top: 2rem;
}

.footerCopyright {
  color: var(--text-tertiary);
  font-size: 0.8rem;
  margin: 0;
}

.footerLegal {
  display: flex;
  gap: 2rem;
}

.legalLink {
  color: var(--text-tertiary);
  text-decoration: none;
  font-size: 0.8rem;
  transition: color 0.3s ease;
}

.legalLink:hover {
  color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .footerAnimation {
    left: -25vw;
    width: 50vw;
  }

  .footerContentWrapper {
    max-width: 450px;
    padding: 3rem 2rem 3rem 3rem;
  }
}

@media (max-width: 1024px) {
  .footerAnimation {
    left: -30vw;
    width: 40vw;
  }

  .footerContentWrapper {
    max-width: 400px;
    padding: 3rem 1.5rem;
  }

  .footerGrid {
    gap: 2.5rem;
  }
}

@media (max-width: 768px) {
  .footer {
    min-height: 350px;
  }

  .footerMainContent {
    min-height: 350px;
  }

  .footerAnimation {
    left: -35vw;
    width: 40vw;
  }

  .footerContentWrapper {
    max-width: 350px;
    padding: 2.5rem 1rem;
  }

  .footerGrid {
    gap: 2rem;
  }

  .footerTagline {
    max-width: 100%;
    font-size: 0.8rem;
  }

  .socialIcons {
    gap: 0.5rem;
  }

  .socialLink {
    width: 36px;
    height: 36px;
  }

  .socialLink i {
    font-size: 0.875rem;
  }

  .footerBottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .footerLegal {
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .footer {
    min-height: 300px;
  }

  .footerMainContent {
    min-height: 300px;
  }

  .footerAnimation {
    left: -40vw;
    width: 40vw;
  }

  .footerContentWrapper {
    max-width: 280px;
    padding: 2rem 0.75rem;
  }

  .footerGrid {
    gap: 1.5rem;
  }

  .footerBrand {
    gap: 0.5rem;
  }

  .footerLogo {
    width: 40px;
    height: 40px;
    border-radius: 10px;
  }

  .logoText {
    font-size: 1rem;
  }

  .brandName {
    font-size: 1.125rem;
  }

  .footerTagline {
    font-size: 0.75rem;
    max-width: 250px;
  }

  .socialIcons {
    gap: 0.375rem;
  }

  .socialLink {
    width: 32px;
    height: 32px;
    border-radius: 8px;
  }

  .socialLink i {
    font-size: 0.8rem;
  }

  .columnTitle {
    font-size: 0.7rem;
  }

  .footerLink {
    font-size: 0.8rem;
  }

  .linksList {
    gap: 0.4rem;
  }

  .footerBottom {
    padding-top: 1.5rem;
    gap: 0.75rem;
    flex-direction: column;
    text-align: left;
  }

  .footerCopyright {
    font-size: 0.75rem;
  }

  .footerLegal {
    gap: 1rem;
  }

  .legalLink {
    font-size: 0.75rem;
  }
}

/* Accessibility and Motion Preferences */
@media (prefers-reduced-motion: reduce) {
  .footerBrand,
  .socialLink,
  .footerLink,
  .lottieAnimation {
    transition: none;
  }

  .footerBrand:hover,
  .socialLink:hover {
    transform: none;
  }

  .footerLink:hover {
    transform: none;
  }

  .footerLink::after {
    transition: none;
  }

  .lottieAnimation {
    animation: none;
  }
}

@media (prefers-contrast: high) {
  .footer {
    border-top: 2px solid var(--border-strong);
  }

  .socialLink {
    border: 2px solid var(--border-strong);
  }

  .footerBottom {
    border-top: 2px solid var(--border-strong);
  }
}