.toolsSection {
  padding: var(--space-12)0;
  background-color: var(--bg-dark);
  border-top: 1px solid var(--border-light);
  border-bottom: 1px solid var(--border-light);
  position: relative;
  overflow: hidden
}

.toolsHeader {
  text-align: center;
  margin-bottom: var(--space-8)
}

.toolsTitle {
  font-size: var(--text-2xl);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin: 0;
  margin-bottom: var(--space-4)
}

.toolsSubtitle {
  font-size: var(--text-base);
  color: var(--text-tertiary);
  max-width: 600px;
  margin: 0 auto
}

.toolsContainer {
  width: 100%;
  position: relative;
  overflow: hidden
}

.scrollRow {
  display: flex;
  flex-direction: row;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  -webkit-overflow-scrolling: touch;
  gap: var(--space-8);
  padding: var(--space-4)0;
  align-items: center
}

.toolItem, .toolLogoContainer {
  display: flex;
  align-items: center
}

.toolItem {
  flex-direction: column;
  transition: transform var(--transition-normal), opacity var(--transition-normal);
  scroll-snap-align: center;
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 var(--space-4)
}

.toolItem:hover {
  transform: translateY(-5px);
  opacity: 1
}

.toolLogoContainer {
  height: 40px;
  width: auto;
  justify-content: center;
  margin-bottom: var(--space-3);
  transition: transform var(--transition-normal);
  position: relative;
  overflow: visible;
  display: flex;
  align-items: center
}

.toolLogo {
  height: 40px !important;
  width: auto !important;
  filter: grayscale(1) brightness(200%) contrast(200%);
  /* opacity: .2; */
  object-fit: contain;
  transform: translateZ(0);
}

.toolItem:hover .toolLogo {
  filter: unset;
  opacity: 1
}

.toolLogoPlaceholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--primary)
}

.toolName {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  text-align: center;
  transition: color var(--transition-normal);
  margin-top: var(--space-2);
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis
}

.toolItem:hover .toolName {
  color: var(--text-primary)
}

.toolsGradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, var(--section-bg-testimonials), transparent 10%, transparent 90%, var(--section-bg-testimonials));
  pointer-events: none;
  z-index: 2
}

@media (max-width:768px) {
  .toolsSection {
    padding: var(--space-8)0
  }

  .scrollRow {
    gap: var(--space-4)
  }

  .toolItem {
    min-width: 50px;
    max-width: 100px
  }

  .toolLogoContainer {
    height: 35px
  }

  .toolLogo {
    height: 35px !important
  }

  .toolName {
    font-size: var(--text-xs)
  }
}

@media (max-width:480px) {
  .scrollRow {
    gap: var(--space-12)
  }

  .toolItem {
    min-width: 45px;
    max-width: 90px
  }

  .toolLogoContainer {
    height: 30px
  }

  .toolLogo {
    height: 30px !important
  }
}

.marqueeTrack {
  display: flex;
  width: max-content
}