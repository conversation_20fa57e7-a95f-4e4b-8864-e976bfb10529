.stats-wrapper {
  --stats-animation-progress: 0;
  padding: 0 0 var(--section-spacing-y);
  background-color: var(--surface-dim);
  margin-top: 0;
}

.animation-viewport {
  --progress: var(--stats-animation-progress, 0);
  width: 100vw;
  margin-left: calc(50% - 50vw);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(420px + (100vh - 420px) * (1 - var(--progress)));
  padding: clamp(1rem, 6vw, 4rem) 0;
}

.stats-animation {
  --progress: var(--stats-animation-progress, 0);
  width: calc(var(--content-inner) + (100vw - var(--content-inner)) * (1 - var(--progress)));
  max-width: 100vw;
  height: calc(360px + (100vh - 360px) * (1 - var(--progress)));
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: calc(3rem * var(--progress));
  overflow: hidden;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 3rem;
  max-width: var(--content-inner);
  margin: 3rem auto;
  padding: 0 2rem;
}

.stat-card {
  text-align: center;
}

.animated-counter {
  font-size: 3.5rem;
  font-weight: 700;
  color: var(--white);
  margin-bottom: 1rem;
  position: relative;
}

.animated-counter::after {
  position: absolute;
  font-size: 2.5rem;
  margin-left: 0.2rem;
}

.stat-label {
  font-size: 1.2rem;
  color: var(--cw-3);
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
