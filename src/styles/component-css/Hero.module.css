.heroSection {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: var(--section-spacing-y-lg)0;
  background-color: var(--bg);
  contain: layout size paint
}

.heroContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rem
}

.heroContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 4xl;
  gap: 2rem
}

.aiBadge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: .75rem;
  padding: .875rem 1.75rem;
  background: linear-gradient(120deg, rgba(0, 236, 240, .12), rgba(61, 158, 238, .08));
  border: 1px solid rgba(61, 158, 238, .35);
  border-radius: 9999px;
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  box-shadow: 0 20px 45px rgba(0, 0, 0, .25);
  position: relative;
  overflow: hidden;
  transition: border-color .3s ease, box-shadow .3s ease, background .3s ease
}

.aiBadge::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  border: 1px solid rgba(255, 255, 255, .08);
  pointer-events: none;
  mix-blend-mode: screen
}

.aiBadge:hover {
  border-color: rgba(61, 158, 238, .55);
  box-shadow: 0 24px 60px rgba(0, 0, 0, .3);
  background: linear-gradient(120deg, rgba(0, 236, 240, .18), rgba(61, 158, 238, .12))
}

.aiBadgeLabel {
  display: inline-flex;
  align-items: center;
  gap: .625rem;
  font-size: .75rem;
  letter-spacing: var(--tracking-widest);
  font-weight: 600;
  text-transform: uppercase
}

.aiBadgeCopy {
  background: linear-gradient(90deg, var(--accent-green), var(--color-blue-light));
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent
}

.aiBadgeIndicator {
  display: inline-flex;
  width: .625rem;
  height: .625rem;
  border-radius: 9999px;
  background: radial-gradient(circle, var(--accent-green) 0%, var(--accent-teal) 70%);
  box-shadow: 0 0 0 0 rgba(25, 253, 178, .6);
  animation: heroBadgePulse 2.8s ease-in-out infinite;
  flex-shrink: 0
}

@keyframes heroBadgePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(25, 253, 178, .45)
  }

  70% {
    box-shadow: 0 0 0 10px transparent
  }

  to {
    box-shadow: 0 0 0 0 transparent
  }
}

.heroHeading {
  font-family: var(--font-bwgradual);
  font-size: var(--text-display);
  font-weight: 200;
  line-height: var(--leading-tight);
  color: var(--fg);
  margin: 0;
  letter-spacing: -.02em
}

.heroHeadingThin {
  font-weight: 700
}

.heroSubheading {
  font-size: 1.25rem;
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  max-width: 600px;
  margin: 0
}

.heroActions {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-top: 1rem
}

.trustStrip {
  margin-top: 2rem
}

.trustPill {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1rem 2rem;
  background: var(--surface-dim);
  border: 1px solid var(--border-soft);
  border-radius: 2rem;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px)
}

.trustText {
  font-size: .875rem;
  color: var(--text-tertiary);
  font-weight: 500
}

.trustLogos {
  display: flex;
  align-items: center;
  gap: 1rem
}

.trustLogo {
  height: 32px;
  width: auto;
  opacity: .7;
  filter: saturate(0);
  transition: .3s ease
}

.trustLogo:hover {
  opacity: 1;
  filter: saturate(1)
}

.trustLogoZetahash {
  border-radius: 100px;
  padding: 0 10px;
  background-color: #0062ff
}

.trustLogoDvote {
  max-height: 1.2rem
}

.heroStats {
  display: flex;
  gap: 1.5rem;
  width: 100%;
  max-width: 800px;
  justify-content: center;
  flex-wrap: wrap;
  padding: 0 1rem;
  position: relative
}

.heroStats::after {
  content: "";
  position: absolute;
  bottom: -2rem;
  left: 0;
  right: 0;
  height: 2rem;
  background: linear-gradient(to bottom, transparent, var(--bg));
  pointer-events: none;
  z-index: 1
}

.statCard {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.75rem 1.25rem;
  background: var(--surface-dim);
  border: 1px solid var(--border-soft);
  border-radius: 1.5rem;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  flex: 1;
  min-width: 200px;
  max-width: 240px;
  min-height: 130px;
  position: relative;
  transition: all .3s ease
}

.statCard:hover {
  background: rgba(255, 255, 255, .05);
  border-color: var(--border-strong);
  transform: translateY(-4px);
  box-shadow: 0 20px 60px -40px rgba(47, 129, 247, .9)
}

.statNumber {
  font-family: var(--font-bwgradual);
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--fg);
  margin-bottom: .5rem;
  letter-spacing: -.02em
}

.statDivider {
  width: 2rem;
  height: 1px;
  background: var(--border-soft);
  margin: .5rem 0
}

.statLabel {
  font-size: .875rem;
  color: var(--text-secondary);
  text-align: center;
  line-height: 1.4;
  font-weight: 500
}

.heroBackground {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
  position: absolute !important
}

.heroBackground svg {
  background: 0 0 !important
}

.heroPattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  opacity: .5
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px)
  }

  to {
    opacity: 1;
    transform: translateY(0)
  }
}

@keyframes occasionalWobble {
  0%, 85% {
    transform: rotate(0deg)
  }

  87% {
    transform: rotate(1deg)
  }

  89% {
    transform: rotate(-1deg)
  }

  91% {
    transform: rotate(0.5deg)
  }

  93% {
    transform: rotate(-0.5deg)
  }

  95%, 100% {
    transform: rotate(0deg)
  }
}

@media (max-width:1024px) {
  .heroHeading {
    font-size: clamp(2.5rem, 4vw, 3.5rem)
  }

  .heroContainer {
    gap: 6rem
  }

  .heroStats {
    gap: 1.25rem;
    max-width: 700px
  }

  .statCard {
    padding: 1.5rem 1rem;
    min-height: 115px;
    min-width: 180px;
    max-width: 220px
  }

  .statNumber {
    font-size: 2rem
  }
}

@media (max-width:900px) {
  .heroStats {
    max-width: 600px;
    gap: 1rem
  }

  .statCard {
    min-width: 160px;
    max-width: 200px;
    padding: 1.25rem .875rem;
    min-height: 110px
  }

  .statNumber {
    font-size: 1.875rem
  }

  .statLabel {
    font-size: .8rem
  }
}

@media (max-width:768px) {
  .heroSection {
    padding: calc(var(--section-spacing-y) + 2rem)0 var(--section-spacing-y);
    min-height: calc(100vh - 4rem)
  }

  .heroContainer {
    gap: 4rem
  }

  .heroContent {
    gap: 1.5rem
  }

  .heroHeading {
    font-size: clamp(2rem, 6vw, 2.75rem);
    line-height: 1.2
  }

  .heroSubheading {
    font-size: 1.125rem
  }

  .heroActions {
    flex-direction: column;
    width: 100%;
    max-width: 320px;
    gap: 1rem
  }

  .heroActions a {
    width: 100%;
    justify-content: center
  }

  .trustPill {
    flex-direction: column;
    gap: 1rem;
    padding: 1.5rem;
    text-align: center
  }

  .heroStats {
    flex-direction: column;
    max-width: 340px;
    width: 100%;
    gap: 1rem;
    padding: 0
  }

  .statCard {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 1.5rem;
    min-height: 75px;
    min-width: auto;
    max-width: none
  }

  .statNumber {
    font-size: 1.75rem;
    margin-bottom: 0
  }

  .statDivider {
    display: none
  }

  .statLabel {
    text-align: right;
    max-width: 65%;
    font-size: .8rem;
    line-height: 1.3
  }
}

@media (max-width:480px) {
  .heroHeading {
    font-size: clamp(1.75rem, 8vw, 2.25rem)
  }

  .heroSubheading {
    font-size: 1rem
  }

  .aiBadge, .trustPill {
    padding: 1rem 1.5rem
  }

  .heroStats {
    max-width: 300px
  }

  .statCard {
    padding: 1rem 1.25rem;
    min-height: 65px
  }

  .statNumber {
    font-size: 1.5rem
  }

  .statLabel {
    font-size: .75rem;
    max-width: 70%
  }
}

@media (prefers-reduced-motion:reduce) {
  .heroContent, .statCard, .trustLogo, .themedLottie {
    animation: none;
    transition: none
  }
}

/* Lottie animation styles */
.themedLottie {
  animation: occasionalWobble 8s ease-in-out infinite;
  transform-origin: center center;
}

/* Dark theme styles */
.themedLottie [fill="rgb(230,230,230)"],
.themedLottie [style*="fill:rgb(230,230,230)"] {
  fill: var(--border-light) !important;
}

/* Dark theme styles */
.themedLottie [fill="rgb(0,0,0)"],
.themedLottie [style*="fill:rgb(0,0,0)"] {
  fill: var(--grey) !important;
}
