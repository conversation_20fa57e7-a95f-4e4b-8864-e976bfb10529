.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  padding: 1.5rem 0;
  transition: all .3s ease;
  min-height: 88px;
  display: flex;
  align-items: center;
  color: var(--current-section-text, var(--text-primary));
}

.scrolled {
  padding: .75rem 0
}

.scrolled .container {
  padding: .75rem 1rem .75rem 2rem;
  border-radius: 100px;
  /* background: rgba(11, 13, 18, .85); */
  box-shadow: 0 4px 30px rgba(0, 0, 0, .2);
  backdrop-filter: blur(16px);
}

.scrolled :global(.btn-primary) {
  border-radius: 100px
}

.container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  transition: all .3s ease;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto
}

header:not(.scrolled) .container {
  max-width: 100%
}

.brand {
  display: flex;
  align-items: center;
  text-decoration: none;
  transition: transform .3s ease;
  z-index: 1001
}

.brand:hover {
  transform: translateY(-2px)
}

.brand:focus {
  outline: 0;
  box-shadow: 0 0 0 3px rgba(25, 253, 178, .3);
  border-radius: 4px
}

.logo {
  width: 32px;
  height: 32px;
  border-radius: 100%;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  box-shadow: 0 4px 20px rgba(25, 253, 178, .3);
  position: relative;
  overflow: hidden;
  flex-shrink: 0
}

.logo img {
  width: 100%;
  height: 100%;
  border-radius: inherit
}

.logo::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, .3)0, transparent 70%);
  opacity: 0;
  transition: opacity .3s ease
}

.brand:hover .logo::after {
  opacity: 1
}

.brandName {
  font-size: 1.8rem;
  font-weight: 800;
  font-family: var(--font-bwgradual);
  background: linear-gradient(to left, var(--secondary), var(--primary));
  background-clip: text;
  color: transparent;
  text-shadow: 0 2px 10px rgba(0, 0, 0, .1);
  line-height: 1.9
}

.aiBadge {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  background: var(--surface-dim);
  border: 1px solid var(--border-soft);
  border-radius: 2rem;
  padding: .5rem 1rem;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px)
}

.desktopNav {
  display: flex;
  align-items: center;
  gap: 2rem
}

.menu {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem
}

.link {
  color: inherit;
  opacity: .85;
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  position: relative;
  padding: .5rem 0;
  transition: opacity .3s ease;
  background: 0 0;
  border: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: .5rem
}

.link::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary);
  transition: width .3s ease
}

.link:hover {
  opacity: 1
}

.link:hover::after {
  width: 100%
}

.link:focus {
  outline: 0;
  opacity: 1
}

.link:focus::after {
  width: 100%
}

.servicesDropdown {
  position: relative
}

.servicesButton {
  background: 0 0;
  border: 0;
  cursor: pointer
}

.chevron {
  transition: transform .3s ease;
  font-size: .875rem
}

.chevronOpen {
  transform: rotate(180deg)
}

.megaMenu {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 800px;
  background: var(--surface-glass);
  border: 1px solid var(--border-soft);
  border-radius: 1.5rem;
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  box-shadow: 0 20px 60px -40px rgba(47, 129, 247, .9);
  margin-top: 1rem;
  z-index: 1000;
  animation: fadeInUp .2s ease-out
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px)
  }

  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0)
  }
}

.megaMenuContent {
  padding: 2rem
}

.servicesGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 2rem
}

.serviceCard {
  padding: 1.5rem;
  border-radius: 1rem;
  background: var(--surface-dim);
  border: 1px solid var(--border-soft);
  text-decoration: none;
  transition: all .3s ease
}

.serviceCard:hover {
  background: rgba(255, 255, 255, .05);
  border-color: var(--border-strong);
  transform: translateY(-2px)
}

.serviceTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: inherit;
  margin-bottom: .5rem;
  font-family: var(--font-bwgradual)
}

.serviceDescription {
  font-size: .875rem;
  color: inherit;
  opacity: .75;
  line-height: 1.5;
  margin: 0
}

.megaMenuFooter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-soft)
}

.allServicesLink {
  color: inherit;
  opacity: .85;
  text-decoration: none;
  font-size: .875rem;
  transition: color .3s ease, opacity .3s ease
}

.allServicesLink:hover {
  color: var(--primary);
  opacity: 1
}

.themeSwitcher {
  margin-left: 1.5rem
}

.themeButton {
  padding: .5rem;
  border-radius: .5rem;
  background: 0 0;
  border: 1px solid transparent;
  transition: all .3s ease
}

.themeButton:hover {
  background: var(--surface-dim);
  border-color: var(--border-soft)
}

.cta {
  margin-left: 1.5rem
}

.menuToggle {
  display: none;
  background: 0 0;
  border: 0;
  cursor: pointer;
  padding: .5rem;
  z-index: 1001;
  border-radius: 4px;
  transition: background-color .3s ease;
  color: inherit
}

.menuToggle:focus {
  outline: 0;
  box-shadow: 0 0 0 3px rgba(25, 253, 178, .3)
}

.menuToggle:hover {
  background-color: rgba(255, 255, 255, .1)
}

.hamburger, .hamburger::before, .hamburger::after {
  width: 24px;
  height: 2px;
  background-color: currentColor;
  transition: all .3s ease
}

.hamburger {
  display: block;
  position: relative
}

.hamburger::before, .hamburger::after {
  content: "";
  position: absolute
}

.hamburger::before {
  top: -8px
}

.hamburger::after {
  bottom: -8px
}

.hamburgerOpen {
  background-color: transparent
}

.hamburgerOpen::before {
  transform: rotate(45deg);
  top: 0
}

.hamburgerOpen::after {
  transform: rotate(-45deg);
  bottom: 0
}

.mobileMenu {
  position: fixed;
  top: 0;
  right: -100%;
  width: 340px;
  height: 100vh;
  background: var(--surface-glass);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border-left: 1px solid var(--border-soft);
  transition: right .3s ease;
  z-index: 1000;
  overflow-y: auto
}

.mobileMenuOpen {
  right: 0
}

.mobileMenuContent {
  padding: 6rem 2rem 2rem;
  height: 100%;
  display: flex;
  flex-direction: column
}

.mobileMenuList {
  list-style: none;
  margin: 0;
  padding: 0;
  flex: 1
}

.mobileMenuList li {
  margin-bottom: .5rem
}

.mobileLink {
  display: flex;
  flex-direction: column;
  padding: 1rem 0;
  text-decoration: none;
  border-bottom: 1px solid var(--border-soft);
  transition: all .3s ease
}

.mobileLink:hover {
  background: var(--surface-dim);
  margin: 0-1rem;
  padding-left: 1rem;
  padding-right: 1rem;
  border-radius: .5rem;
  border-bottom-color: transparent
}

.mobileLinkLabel {
  font-size: 1.125rem;
  font-weight: 600;
  color: inherit;
  margin-bottom: .25rem;
  text-transform: uppercase;
  letter-spacing: .05em;
  font-family: var(--font-bwgradual)
}

.mobileLinkDescription {
  font-size: .875rem;
  color: inherit;
  opacity: .75
}

.mobileCTAGroup {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-soft)
}

.emailLink {
  display: block;
  text-align: center;
  margin-top: 1rem;
  color: inherit;
  opacity: .85;
  text-decoration: none;
  font-size: .875rem;
  transition: color .3s ease, opacity .3s ease
}

.emailLink:hover {
  color: var(--primary);
  opacity: 1
}

@media (max-width:1023px) {
  .menuToggle {
    display: block
  }

  .aiBadge {
    display: none
  }
}

@media (max-width:768px) {
  .navbar {
    padding: 1rem 0
  }

  .scrolled {
    padding: .5rem 0
  }

  .brandName {
    font-size: 1.5rem
  }

  .logo {
    width: 36px;
    height: 36px
  }

  .mobileMenu {
    width: 100%
  }
}
